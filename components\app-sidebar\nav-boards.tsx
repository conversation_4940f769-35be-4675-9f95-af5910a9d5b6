"use client"

// 路由组件
import { Link } from 'react-router-dom';

// lucide图标
import { ChevronRight, SquareTerminal, Plus, X } from "lucide-react"

// shadcn/ui组件
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuAction,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import { Button } from '@/components/ui/button';

// 单板导航组件
export function NavBoards({
  conn_types, activeItem, setActiveItem
}: {
  conn_types: {
    title: string
    path: string
  }[],
  activeItem: string,
  setActiveItem: (item: string) => void,
}) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>主控/单板串口</SidebarGroupLabel>
      <SidebarMenu>
      {conn_types.map((item) => (
          <SidebarMenuItem key={item.title} onClick={() => setActiveItem(item.title)}>
            <Link to={item.path} className="flex items-center w-full h-full">
              <SidebarMenuButton tooltip={item.title} isActive={activeItem === item.title}>
                <SquareTerminal />
                <span>{item.title}</span>
              </SidebarMenuButton>
              <SidebarMenuAction showOnHover>
                <X />
              </SidebarMenuAction>
            </Link>
          </SidebarMenuItem>
        ))}
        <SidebarMenuItem>
          <SidebarMenuButton className="text-sidebar-foreground/70">
            <Plus className="text-sidebar-foreground/70" />
            <span>打开新的串口...</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  )
}
