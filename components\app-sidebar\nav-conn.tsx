"use client"

import React, { useState } from 'react';

// lucide图标
import { PlusCircleIcon, Unplug, SignalLow, ChevronsLeftRightEllipsis } from "lucide-react"

// shadcn/ui组件
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroupContent
} from "@/components/ui/sidebar"

type ConnState = "disconnected" | "connecting" | "connected" | "disconnecting";

// 连接状态切换器
export function ConnSwitcher() {
  const [connState, setConnState] = useState<ConnState>("disconnected");

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <SidebarMenuButton
              tooltip="Quick Create"
              className="min-w-8 bg-primary text-primary-foreground duration-200 ease-linear hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground"
              onClick={() => {
                if (connState === "disconnected") {
                  setConnState("connecting");
                } else if (connState === "connecting") {
                  setConnState("connected");
                } else if (connState === "connected") {
                  setConnState("disconnecting");
                } else if (connState === "disconnecting") {
                  setConnState("disconnected");
                }
              }}
            >
              {connState === "disconnected" ? (
                <>
                  <Unplug />
                  <span>尚未连接</span>
                </>
              ) : (
                connState === "connecting" ? (
                  <>
                    <SignalLow />
                    <span>连接中...</span>
                  </>
                ) : (
                  connState === "connected" ? (
                    <>
                      <ChevronsLeftRightEllipsis />
                      <span>已连接</span>
                    </>
                  ) : (
                    <>
                      <SignalLow />
                      <span>断开中...</span>
                    </>
                  )
                )
              )}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
