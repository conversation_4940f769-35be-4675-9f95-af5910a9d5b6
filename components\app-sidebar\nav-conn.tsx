"use client"

import React, { useState, useEffect } from 'react';

// lucide图标
import { Unplug, SignalLow, ChevronsLeftRightEllipsis } from "lucide-react"

// shadcn/ui组件
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroupContent
} from "@/components/ui/sidebar"

// 工具函数
import { connectTcpServer, disconnectTcpServer } from "@/components/utils/env-utils"

// hooks
import { useToast } from "@/hooks/use-toast"

// 类型定义
import type { Environment } from "@/types/env"

type ConnState = "disconnected" | "connecting" | "connected" | "disconnecting";

interface ConnSwitcherProps {
  activeEnv: Environment | null;
  onConnectionChange?: (connectionId: string | null) => void;
}

// 连接状态切换器
export function ConnSwitcher({ activeEnv, onConnectionChange }: ConnSwitcherProps) {
  const [connState, setConnState] = useState<ConnState>("disconnected");
  const [connectionId, setConnectionId] = useState<string | null>(null);
  const { toast } = useToast();

  // 当活动环境改变时，断开当前连接
  useEffect(() => {
    if (connectionId && connState === "connected") {
      handleDisconnect();
    }
  }, [activeEnv?.id]);

  // 连接到服务器
  const handleConnect = async () => {
    if (!activeEnv) {
      toast({
        title: "连接失败",
        description: "请先选择一个环境",
        variant: "destructive",
      });
      return;
    }

    setConnState("connecting");

    try {
      const connId = await connectTcpServer(activeEnv.addr, Number(activeEnv.port));
      setConnectionId(connId);
      setConnState("connected");
      onConnectionChange?.(connId);

      toast({
        title: "连接成功",
        description: `已连接到 ${activeEnv.name} (${activeEnv.addr}:${activeEnv.port})`,
        duration: 2000,
      });
    } catch (error) {
      setConnState("disconnected");
      toast({
        title: "连接失败",
        description: `无法连接到 ${activeEnv.name}: ${error}`,
        variant: "destructive",
      });
    }
  };

  // 断开连接
  const handleDisconnect = async () => {
    if (!connectionId) return;

    setConnState("disconnecting");

    try {
      await disconnectTcpServer(connectionId);
      setConnectionId(null);
      setConnState("disconnected");
      onConnectionChange?.(null);

      toast({
        title: "已断开连接",
        description: activeEnv ? `已断开与 ${activeEnv.name} 的连接` : "连接已断开",
        duration: 2000,
      });
    } catch (error) {
      setConnState("connected"); // 回滚状态
      toast({
        title: "断开连接失败",
        description: `${error}`,
        variant: "destructive",
      });
    }
  };

  // 处理点击事件
  const handleClick = () => {
    if (connState === "disconnected") {
      handleConnect();
    } else if (connState === "connected") {
      handleDisconnect();
    }
    // 连接中和断开中状态不响应点击
  };

  // 获取按钮文本和图标
  const getButtonContent = () => {
    switch (connState) {
      case "disconnected":
        return {
          icon: <Unplug />,
          text: activeEnv ? `连接到 ${activeEnv.name}` : "尚未连接"
        };
      case "connecting":
        return {
          icon: <SignalLow className="animate-pulse" />,
          text: "连接中..."
        };
      case "connected":
        return {
          icon: <ChevronsLeftRightEllipsis />,
          text: activeEnv ? `已连接 ${activeEnv.name}` : "已连接"
        };
      case "disconnecting":
        return {
          icon: <SignalLow className="animate-pulse" />,
          text: "断开中..."
        };
    }
  };

  const { icon, text } = getButtonContent();
  const isDisabled = connState === "connecting" || connState === "disconnecting";

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <SidebarMenuButton
              tooltip={text}
              className="min-w-8 bg-primary text-primary-foreground duration-200 ease-linear hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleClick}
              disabled={isDisabled}
            >
              {icon}
              <span>{text}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
