// ./components/zshell/editor/hooks/useEditorActions.ts
import { useCallback, useRef, useEffect } from 'react';
import * as monaco from 'monaco-editor';
import type { editor } from 'monaco-editor';
import type { Tab } from '../types';
import { sendTcpData } from '@/components/utils/env-utils';
import { useToast } from '@/hooks/use-toast';

/**
 * Hook 属性接口
 */
interface UseEditorActionsProps {
  tabs: Tab[];
  activeTabId: string | null;
  editorsRef: React.MutableRefObject<Record<string, editor.IStandaloneCodeEditor>>;
  saveActiveFile: (tabs: Tab[], activeTabId: string | null) => Promise<boolean>;
  toggleHighlightAtCursor: () => void;
  clearAllHighlights: () => void;
  updateTabContent: (tabId: string, content: string) => void;
  connectionId?: string | null;
  boardId?: string;
}

/**
 * Hook: 管理编辑器快捷键和操作
 *
 * 负责定义和添加编辑器的快捷键操作，如保存文件、高亮等
 * 使用引用模式确保快捷键操作始终使用最新的状态
 */
export function useEditorActions({
  tabs,
  activeTabId,
  editorsRef,
  saveActiveFile,
  toggleHighlightAtCursor,
  clearAllHighlights,
  connectionId,
  boardId,
}: UseEditorActionsProps) {
  const { toast } = useToast();

  // 使用ref存储当前的action处理函数，确保快捷键操作始终使用最新的状态
  const actionsRef = useRef<{
    saveFile: () => Promise<void>;
    toggleHighlight: () => void;
    clearHighlights: () => void;
    sendCurrentLine: () => Promise<void>;
  }>({
    saveFile: async () => {},
    toggleHighlight: () => {},
    clearHighlights: () => {},
    sendCurrentLine: async () => {}
  });

  // 每次依赖项变化时，更新actionsRef中的函数
  useEffect(() => {
    console.log("更新编辑器操作函数引用:");
    tabs.forEach(tab => console.log(tab));

    actionsRef.current = {
      saveFile: async () => {
        if (!activeTabId) return;
        await saveActiveFile(tabs, activeTabId);
      },
      toggleHighlight: () => toggleHighlightAtCursor(),
      clearHighlights: () => clearAllHighlights(),
      sendCurrentLine: async () => {
        if (!connectionId) {
          toast({
            title: "发送失败",
            description: "请先连接到服务器",
            variant: "destructive",
          });
          return;
        }

        if (!activeTabId || !editorsRef.current[activeTabId]) {
          toast({
            title: "发送失败",
            description: "没有活动的编辑器",
            variant: "destructive",
          });
          return;
        }

        const editor = editorsRef.current[activeTabId];
        const position = editor.getPosition();
        if (!position) return;

        const model = editor.getModel();
        if (!model) return;

        // 获取当前行的内容
        const lineContent = model.getLineContent(position.lineNumber).trim();
        if (!lineContent) {
          toast({
            title: "发送失败",
            description: "当前行为空",
            variant: "destructive",
          });
          return;
        }

        try {
          // 将文本转换为十六进制
          const hexData = Array.from(new TextEncoder().encode(lineContent))
            .map(byte => byte.toString(16).padStart(2, '0'))
            .join('');

          // 发送数据到服务器
          if (boardId) {
            // 如果有单板ID，需要将单板ID转换为十六进制格式
            // 后端期望单板ID是3个字符，转换为6个十六进制字符
            const boardIdHex = Array.from(new TextEncoder().encode(boardId.padEnd(3, '0')))
              .map(byte => byte.toString(16).padStart(2, '0'))
              .join('');

            // 使用单板发送接口
            const { sendTcpDataBoard } = await import('@/components/utils/env-utils');
            await sendTcpDataBoard(connectionId, hexData, boardIdHex);
          } else {
            // 否则使用普通发送接口
            await sendTcpData(connectionId, hexData);
          }

          toast({
            title: "发送成功",
            description: `已发送: ${lineContent}`,
            duration: 2000,
          });
        } catch (error) {
          toast({
            title: "发送失败",
            description: `${error}`,
            variant: "destructive",
          });
        }
      }
    };
  }, [tabs, activeTabId, saveActiveFile, toggleHighlightAtCursor, clearAllHighlights, connectionId, boardId, editorsRef, toast]);

  /**
   * 为 Monaco 编辑器实例初始化自定义操作 (快捷键)
   * 这个函数只在编辑器挂载时调用一次
   */
  const initEditorActions = useCallback((editor: editor.IStandaloneCodeEditor) => {
    console.log("初始化编辑器快捷键操作");

    // 保存文件 (Ctrl/Cmd + S)
    editor.addAction({
      id: 'save-file',
      label: '保存文件',
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
      run: async () => {
        try {
          // 调用ref中的最新函数，而不是闭包中的旧函数
          await actionsRef.current.saveFile();
        } catch (error) {
          console.error('保存文件时出错:', error);
          // 不抛出错误，避免 Monaco 处理异常
        }
      },
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.5
    });

    // 切换高亮 (F8)
    editor.addAction({
      id: 'toggle-highlight',
      label: '切换单词高亮',
      keybindings: [monaco.KeyCode.F8],
      run: () => actionsRef.current.toggleHighlight(),
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.6
    });

    // 清除所有高亮 (Ctrl/Cmd + F8)
    editor.addAction({
      id: 'clear-highlights',
      label: '清除所有高亮',
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.F8],
      run: () => actionsRef.current.clearHighlights(),
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.7
    });

    // 发送当前行到服务器 (F7)
    editor.addAction({
      id: 'send-current-line',
      label: '发送当前行到服务器',
      keybindings: [monaco.KeyCode.F7],
      run: async () => {
        try {
          await actionsRef.current.sendCurrentLine();
        } catch (error) {
          console.error('发送当前行时出错:', error);
        }
      },
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.8
    });

  }, []); // 空依赖数组，确保只创建一次

  return { initEditorActions };
}
