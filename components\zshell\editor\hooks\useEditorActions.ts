// ./components/zshell/editor/hooks/useEditorActions.ts
import { useCallback, useRef, useEffect } from 'react';
import * as monaco from 'monaco-editor';
import type { editor } from 'monaco-editor';
import type { Tab } from '../types';

/**
 * Hook 属性接口
 */
interface UseEditorActionsProps {
  tabs: Tab[];
  activeTabId: string | null;
  editorsRef: React.MutableRefObject<Record<string, editor.IStandaloneCodeEditor>>;
  saveActiveFile: (tabs: Tab[], activeTabId: string | null) => Promise<boolean>;
  toggleHighlightAtCursor: () => void;
  clearAllHighlights: () => void;
  updateTabContent: (tabId: string, content: string) => void;
}

/**
 * Hook: 管理编辑器快捷键和操作
 *
 * 负责定义和添加编辑器的快捷键操作，如保存文件、高亮等
 * 使用引用模式确保快捷键操作始终使用最新的状态
 */
export function useEditorActions({
  tabs,
  activeTabId,
  saveActiveFile,
  toggleHighlightAtCursor,
  clearAllHighlights,
}: UseEditorActionsProps) {
  // 使用ref存储当前的action处理函数，确保快捷键操作始终使用最新的状态
  const actionsRef = useRef<{
    saveFile: () => Promise<void>;
    toggleHighlight: () => void;
    clearHighlights: () => void;
  }>({
    saveFile: async () => {},
    toggleHighlight: () => {},
    clearHighlights: () => {}
  });

  // 每次依赖项变化时，更新actionsRef中的函数
  useEffect(() => {
    console.log("更新编辑器操作函数引用:");
    tabs.forEach(tab => console.log(tab));

    actionsRef.current = {
      saveFile: async () => {
        if (!activeTabId) return;
        await saveActiveFile(tabs, activeTabId);
      },
      toggleHighlight: () => toggleHighlightAtCursor(),
      clearHighlights: () => clearAllHighlights()
    };
  }, [tabs, activeTabId, saveActiveFile, toggleHighlightAtCursor, clearAllHighlights]);

  /**
   * 为 Monaco 编辑器实例初始化自定义操作 (快捷键)
   * 这个函数只在编辑器挂载时调用一次
   */
  const initEditorActions = useCallback((editor: editor.IStandaloneCodeEditor) => {
    console.log("初始化编辑器快捷键操作");

    // 保存文件 (Ctrl/Cmd + S)
    editor.addAction({
      id: 'save-file',
      label: '保存文件',
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
      run: async () => {
        try {
          // 调用ref中的最新函数，而不是闭包中的旧函数
          await actionsRef.current.saveFile();
        } catch (error) {
          console.error('保存文件时出错:', error);
          // 不抛出错误，避免 Monaco 处理异常
        }
      },
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.5
    });

    // 切换高亮 (F8)
    editor.addAction({
      id: 'toggle-highlight',
      label: '切换单词高亮',
      keybindings: [monaco.KeyCode.F8],
      run: () => actionsRef.current.toggleHighlight(),
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.6
    });

    // 清除所有高亮 (Ctrl/Cmd + F8)
    editor.addAction({
      id: 'clear-highlights',
      label: '清除所有高亮',
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.F8],
      run: () => actionsRef.current.clearHighlights(),
      contextMenuGroupId: 'navigation',
      contextMenuOrder: 1.7
    });

  }, []); // 空依赖数组，确保只创建一次

  return { initEditorActions };
}
