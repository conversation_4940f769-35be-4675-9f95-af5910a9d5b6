// src/core/tcp_manager.rs

// 从当前 crate (本项目) 的 core::logger 模块导入 format_log_entry 函数，用于格式化日志条目。
use crate::core::logger::format_log_entry;
// 从当前 crate 的 errors 模块导入 AppError 枚举和 Result 类型别名，用于错误处理。
use crate::errors::{AppError, Result};
// 从当前 crate 的 models 模块导入 TcpDataPayload 结构体，用于定义通过 TCP 发送/接收的数据载荷结构。
use crate::models::TcpDataPayload;
// 从 log crate 导入 error!, info!, warn! 宏，用于不同级别的日志记录。
use log::{error, info, warn};
// 从标准库的 collections 模块导入 HashMap，一个键值对存储结构，用于存储连接。
use std::collections::HashMap;
// 从标准库的 net 模块导入 SocketAddr，用于表示网络套接字地址 (IP地址 + 端口号)。
use std::net::SocketAddr;
// 从标准库的 sync 模块导入 Arc (Atomically Reference Counted)，一种线程安全的引用计数智能指针，允许多个所有者共享数据。
use std::sync::Arc;
// 从 tauri crate 导入 AppHandle (应用句柄，用于与 Tauri 核心交互), Emitter (事件发射器，用于向前端发送事件),
// Runtime (Tauri 运行时特性，这里用作泛型约束), Listener (事件监听器，用于在后端监听前端事件，虽然这里主要用 Emitter)。
use tauri::{AppHandle, Emitter, Runtime, Listener};
// 从 tokio crate 的 io 模块导入异步读写相关的 trait (AsyncReadExt, AsyncWriteExt) 和类型 (ReadHalf, WriteHalf)。
// ReadHalf 和 WriteHalf 用于将一个双向流 (如 TcpStream) 分割成独立的读和写部分。
use tokio::io::{AsyncReadExt, AsyncWriteExt, ReadHalf, WriteHalf};
// 从 tokio crate 的 net 模块导入 TcpStream，代表一个异步的 TCP 连接。
use tokio::net::TcpStream;
// 从 tokio crate 的 sync 模块导入 mpsc (multi-producer, single-consumer) 通道和 Mutex (异步互斥锁)。
// mpsc 用于在异步任务间传递消息，Mutex 用于保护共享数据的并发访问。
use tokio::sync::{mpsc, Mutex};
// 从 uuid crate 导入 Uuid 结构体，用于生成全局唯一的标识符 (UUID)，这里用来作为连接ID。
use uuid::Uuid;

struct ManagedConnection {
    // writer: WriteHalf<TcpStream>
    // 这是 TCP 流的写入半部分。通过它，我们可以异步地向连接发送数据。
    // TcpStream 被 tokio::io::split 分割后，WriteHalf 负责写入。
    writer: WriteHalf<TcpStream>,

    // shutdown_tx: mpsc::Sender<()>
    // 这是一个 mpsc 通道的发送端。用于向负责读取数据的异步任务发送一个关闭信号。
    // `()` 表示发送的是一个空元组，仅作为信号。
    shutdown_tx: mpsc::Sender<()>,

    // reader_handle: tokio::task::JoinHandle<()>
    // 这是 Tokio 异步任务的句柄。这个任务通常是负责从 TCP 连接读取数据的循环。
    // JoinHandle 可以用来等待任务完成或取消任务。
    reader_handle: tokio::task::JoinHandle<()>,
}

#[derive(Clone)] // 允许 TcpManager 实例被克隆。这通常是因为它需要在多个地方共享，例如在 Tauri 的命令处理中。
pub struct TcpManager<R: Runtime> {
    // connections: Arc<Mutex<HashMap<String, ManagedConnection>>>
    // 这是核心数据结构，用于存储所有活动的 TCP 连接。
    // - HashMap: 使用连接ID (String) 作为键，ManagedConnection 实例作为值。
    // - Mutex: 由于可能有多个异步任务同时尝试访问或修改这个 HashMap (例如，新的连接、数据发送、断开连接)，
    //   Mutex 用于确保在任何时候只有一个任务可以访问它，防止数据竞争。
    // - Arc: Arc (Atomic Reference Counter) 允许多个所有者安全地共享这个 Mutex 包裹的 HashMap。
    //   当 TcpManager 被克隆时，它们都将共享同一个连接池。
    connections: Arc<Mutex<HashMap<String, ManagedConnection>>>,

    // app_handle: AppHandle<R>
    // 这是 Tauri 应用的句柄。它允许后端代码与 Tauri 系统交互，例如向前端发送事件。
    // 泛型参数 R: Runtime 表示这个 TcpManager 可以与任何实现了 Tauri Runtime trait 的运行时一起工作。
    // 这使得 TcpManager 更加灵活，可以用于不同的 Tauri 配置。
    app_handle: AppHandle<R>,
}

impl<R: Runtime> TcpManager<R> {
    pub fn new(app_handle: AppHandle<R>) -> Self {
        Self {
            // 初始化 connections 字段：
            // 1. HashMap::new() 创建一个新的空哈希表。
            // 2. Mutex::new(...) 将哈希表包裹在异步互斥锁中。
            // 3. Arc::new(...) 将互斥锁包裹的哈希表放入原子引用计数指针中。
            connections: Arc::new(Mutex::new(HashMap::new())),
            // 将传入的 app_handle 存储起来。
            app_handle,
        }
    }

    pub async fn ping(&self, addr: String, port: u16) -> Result<bool> {
        // 将地址字符串和端口号格式化为 "ip:port" 的形式。
        let socket_addr_str = format!("{}:{}", addr, port);
        // 尝试将格式化后的字符串解析为 SocketAddr 类型。
        let socket_addr: SocketAddr = socket_addr_str
            .parse() // String 类型有 parse 方法，可以尝试转换为其他类型。
            // 如果解析失败 (例如，IP地址格式无效)，则使用 map_err 将原始错误转换为自定义的 AppError::InvalidAddress。
            .map_err(|_| AppError::InvalidAddress(socket_addr_str.clone()))?; // `?` 操作符用于错误传播，如果结果是 Err，则提前返回 Err。

        // 使用 tokio::time::timeout 设置一个3秒的超时。
        // TcpStream::connect(socket_addr) 会尝试建立 TCP 连接，这是一个异步操作。
        match tokio::time::timeout(
            std::time::Duration::from_secs(3), // 超时时间
            TcpStream::connect(socket_addr)    // 要执行的异步操作
        ).await { // .await 等待 timeout 操作完成。
            // Timeout 的结果是 Result<Result<TcpStream, std::io::Error>, tokio::time::error::Elapsed>
            // 外层 Result 处理超时本身，内层 Result 处理 TcpStream::connect 的结果。

            // Ok(Ok(_stream)) 表示：
            // 1. Ok(...): 操作在超时时间(3s)内完成了。
            // 2. Ok(_stream): TcpStream::connect 成功建立了连接 (我们用 _stream 忽略连接本身，因为 ping 只关心是否能连上)。
            Ok(Ok(_stream)) => Ok(true), // 连接成功，返回 Ok(true)。

            // Ok(Err(e)) 表示：
            // 1. Ok(...): 操作在超时时间内完成了。
            // 2. Err(e): TcpStream::connect 失败了 (例如，服务器拒绝连接，网络不通等)。
            Ok(Err(e)) => {
                // 记录警告日志，说明 ping 失败的原因。
                warn!("Ping失败 (连接错误) {}:{} - {}", addr, port, e);
                Ok(false) // 连接失败，返回 Ok(false)。
            }

            // Err(_) 表示：
            // 操作超时了 (TcpStream::connect 没有在3秒内完成)。
            Err(_) => {
                // 记录警告日志，说明 ping 因超时而失败。
                warn!("Ping失败 (超时) {}:{}", addr, port);
                Ok(false) // 超时，返回 Ok(false)。
            }
        }
    }

    pub async fn connect(&self, addr: String, port: u16) -> Result<String> {
        // 步骤 1: 解析地址 (与 ping 函数类似)
        let socket_addr_str = format!("{}:{}", addr, port);
        let socket_addr: SocketAddr = socket_addr_str
            .parse()
            .map_err(|_| AppError::InvalidAddress(socket_addr_str.clone()))?;

        // 步骤 2: 建立 TCP 连接
        // TcpStream::connect 是一个异步操作，返回 Result<TcpStream, std::io::Error>。
        // `?` 会在连接失败时提前返回 AppError (因为 std::io::Error 可以通过 `From` trait 转换为 AppError)。
        let stream = TcpStream::connect(socket_addr).await?;

        // 步骤 3: 生成唯一的连接 ID
        // Uuid::new_v4() 生成一个版本4的UUID (随机生成)。
        // .to_string() 将其转换为字符串。
        let conn_id = Uuid::new_v4().to_string();

        // 步骤 4: 分割 TCP 流为读写两半
        // tokio::io::split 将一个双向的 TcpStream 分割成一个 ReadHalf (只能读) 和一个 WriteHalf (只能写)。
        // 这样可以将读写操作分配给不同的任务或逻辑部分。
        let (reader, writer) = tokio::io::split(stream);

        // 步骤 5: 创建用于关闭读取任务的通道
        // mpsc::channel(1) 创建一个异步通道，容量为1。
        // shutdown_tx 是发送端，shutdown_rx 是接收端。
        // 当需要关闭连接的读取部分时，会通过 shutdown_tx 发送一个信号。
        let (shutdown_tx, shutdown_rx) = mpsc::channel(1);

        // 步骤 6: 克隆 AppHandle 和 conn_id 以用于新的异步任务
        // app_handle 和 conn_id 需要被移动到新创建的异步任务中。
        // Arc<T> (AppHandle 内部可能使用 Arc) 和 String 都有 clone 方法，可以创建它们的副本或增加引用计数。
        let app_handle_clone = self.app_handle.clone();
        let conn_id_clone_for_reader = conn_id.clone();

        // 步骤 7: 创建并启动一个异步任务来处理连接的读取
        // tokio::spawn 在 Tokio 运行时上启动一个新的异步任务 (绿色线程)。
        // 这个任务会并发执行，不会阻塞当前 connect 函数的执行。
        let reader_handle = tokio::spawn(async move {
            // async move { ... } 定义了一个异步代码块，并且会捕获其使用的外部变量的所有权。
            // 调用 Self::handle_connection_read_internal 来处理实际的读取逻辑。
            Self::handle_connection_read_internal(
                reader, // TCP 流的读取半部分
                app_handle_clone, // 克隆的应用句柄
                conn_id_clone_for_reader, // 克隆的连接ID
                shutdown_rx, // 关闭信号的接收端
            )
            .await; // 等待读取处理函数完成 (通常是在连接关闭或出错时)。
        });

        // 步骤 8: 创建 ManagedConnection 实例
        let managed_conn = ManagedConnection {
            writer,      // TCP 流的写入半部分
            shutdown_tx, // 关闭信号的发送端
            reader_handle, // 读取任务的句柄
        };

        // 步骤 9: 将新连接存储到共享的连接哈希表中
        // self.connections 是 Arc<Mutex<HashMap<...>>>。
        // .lock().await 获取互斥锁。如果锁已被其他任务持有，则当前任务会异步等待。
        // 一旦获得锁，conns 就是一个 MutexGuard，它允许我们独占访问 HashMap。
        let mut conns = self.connections.lock().await;
        // 将连接ID和 ManagedConnection 实例插入到哈希表中。
        // conn_id.clone() 是因为 conn_id 的所有权在后面日志和返回时还需要。
        conns.insert(conn_id.clone(), managed_conn);
        // MutexGuard (conns) 在离开这个作用域时会自动释放锁。

        // 步骤 10: 记录连接成功的日志
        // format_log_entry 是一个辅助函数，用于统一日志格式。
        info!("{}", format_log_entry(&conn_id, "SYSTEM", format!("Connected to {}:{}", addr, port).as_bytes()));

        // 步骤 11: 返回连接 ID
        Ok(conn_id)
    }

    async fn handle_connection_read_internal(
        mut reader: ReadHalf<TcpStream>, // TCP 流的读取半部分 (可变，因为 read 操作会修改其内部状态)
        app_handle: AppHandle<R>,        // Tauri 应用句柄，用于向前端发送事件
        conn_id: String,                 // 当前连接的唯一ID
        mut shutdown_rx: mpsc::Receiver<()>, // 关闭信号的接收端 (可变)
    ) {
        // 创建一个缓冲区，用于存储从 TCP 流读取的数据。大小为 4096 字节。
        let mut buffer = [0; 4096];

        // 进入一个无限循环，持续尝试读取数据或处理关闭信号。
        loop {
            // tokio::select! 宏允许同时等待多个异步操作，并在其中任何一个完成后执行相应的代码块。
            // 这对于需要同时处理网络IO和内部信号的场景非常有用。
            tokio::select! {
                // biased; 指示 select! 优先检查列在前面的分支。
                // 在这里，我们希望优先检查关闭信号，以便能及时响应关闭请求。
                biased;

                // 分支 1: 等待关闭信号
                // shutdown_rx.recv() 是一个异步操作，它会等待直到从通道接收到消息 (或通道关闭)。
                // `_ = ...` 表示我们不关心接收到的消息内容 (因为它是 `()`)，只关心操作是否完成。
                _ = shutdown_rx.recv() => {
                    // 如果接收到关闭信号 (或通道被关闭，recv() 会返回 None，这也满足这里的条件)。
                    info!("{}", format_log_entry(&conn_id, "SYSTEM", "Reader task shutting down.".as_bytes()));
                    break; // 跳出 loop 循环，结束这个读取任务。
                }

                // 分支 2: 等待从 TCP 流读取数据
                // reader.read(&mut buffer) 是一个异步操作，尝试从 TCP 流读取数据到 buffer 中。
                // 它返回一个 Result<usize, std::io::Error>，其中 usize 是读取到的字节数。
                result = reader.read(&mut buffer) => {
                    match result {
                        // Ok(0) 表示对端 (服务器) 正常关闭了连接的写入端。
                        // 这是 TCP 连接正常关闭的一种方式。
                        Ok(0) => {
                            info!("{}", format_log_entry(&conn_id, "SYSTEM", "Connection closed by peer.".as_bytes()));
                            // 创建一个 TcpDataPayload，通知前端连接已由对端关闭。
                            let payload = TcpDataPayload {
                                id: conn_id.clone(),
                                board_id: "".to_string(),
                                data: "Connection closed by peer".to_string(), // 消息内容
                                timestamp: chrono::Local::now().to_rfc3339(), // 当前时间戳
                            };
                            // 使用 app_handle.emit 向前端发送一个名为 "tcp_closed" 的事件。
                            if let Err(e) = app_handle.emit("tcp_closed", payload.clone()) {
                                error!("发送连接关闭事件到前端失败 for {}: {}", conn_id, e);
                            }
                            break; // 跳出 loop 循环，结束这个读取任务。
                        }
                        // Ok(n) 表示成功读取了 n 字节的数据。
                        Ok(n) => {
                            // 从缓冲区中获取实际读取到的数据部分。
                            let received_data = &buffer[..n];
                            // 提取单板ID和数据
                            let (board_id, data) = Self::extract_board_id_and_data(received_data);
                            // 记录接收到的原始数据 (文本形式)。
                            info!("{}", format_log_entry(&conn_id, "RECV", received_data));

                            // 创建一个 TcpDataPayload，包含接收到的数据。
                            let payload = TcpDataPayload {
                                id: conn_id.clone(),
                                board_id,
                                // 将接收到的字节数据(剔除前6个字符后)进行十六进制编码，转换为字符串。
                                // 这通常是为了方便在前端显示或处理非文本数据。
                                data,
                                timestamp: chrono::Local::now().to_rfc3339(),
                            };
                            // 使用 app_handle.emit 向前端发送一个名为 "tcp_data_received" 的事件。
                            if let Err(e) = app_handle.emit("tcp_data_received", payload.clone()) {
                                error!("发送数据到前端失败 for {}: {}", conn_id, e);
                            }
                        }
                        // Err(e) 表示在读取数据时发生了 I/O 错误。
                        Err(e) => {
                            error!("{}", format_log_entry(&conn_id, "SYSTEM", format!("Read error: {}", e).as_bytes()));
                            // 创建一个 TcpDataPayload，通知前端发生了读取错误。
                             let payload = TcpDataPayload {
                                id: conn_id.clone(),
                                board_id: "".to_string(),
                                data: format!("Read error: {}", e), // 错误信息
                                timestamp: chrono::Local::now().to_rfc3339(),
                            };
                            // 使用 app_handle.emit_to("main", "tcp_error", payload) 向主窗口发送 "tcp_error" 事件。
                            // emit_to 可以指定目标窗口，如果应用有多个窗口的话。
                            if let Err(e_emit) = app_handle.emit_to("main", "tcp_error", payload) {
                                error!("发送错误事件到前端失败 for {}: {}", conn_id, e_emit);
                            }
                            break; // 跳出 loop 循环，结束这个读取任务。
                        }
                    }
                }
            }
        }
        // 循环结束后，记录读取任务已完成。
        info!("Reader task for connection {} finished.", conn_id);
    }

    pub async fn send_data(&self, conn_id: &str, data_hex: &str) -> Result<()> {
        // 步骤 1: 将十六进制字符串解码为字节数据 (Vec<u8>)。
        // hex::decode 会尝试解析 data_hex。如果失败 (例如，包含无效的十六进制字符)，
        // 则 map_err 将其转换为 AppError::HexDecodingError。
        let data = hex::decode(data_hex).map_err(AppError::HexDecodingError)?;

        // 步骤 2: 获取对连接哈希表的可变访问权限。
        // .lock().await 获取互斥锁。注意这里是 conns_guard，它是一个 MutexGuard。
        // 我们需要可变访问权限 (get_mut) 来获取 ManagedConnection 的可变引用，以便调用其 writer 上的方法。
        let mut conns_guard = self.connections.lock().await;

        // 步骤 3: 查找并获取对应的 ManagedConnection。
        // conns_guard.get_mut(conn_id) 尝试从哈希表中获取键为 conn_id 的可变引用。
        if let Some(managed_conn) = conns_guard.get_mut(conn_id) {
            // 如果找到了连接：
            // 步骤 3a: 将所有数据写入 TCP 流的写入半部分。
            // managed_conn.writer.write_all(&data).await 会尝试将 data 中的所有字节写入流。
            // 这是一个异步操作。如果失败，map_err 将错误转换为 AppError::SendDataFailed。
            managed_conn
                .writer
                .write_all(&data)
                .await
                .map_err(|e| AppError::SendDataFailed(format!("Failed to write_all for connection {}: {}", conn_id, e)))?;

            // 步骤 3b: 刷新写入缓冲区，确保数据实际发送出去。
            // managed_conn.writer.flush().await 会尝试将操作系统内核缓冲区中的数据发送到网络。
            // 这是一个异步操作。如果失败，map_err 将错误转换为 AppError::SendDataFailed。
            managed_conn
                .writer
                .flush()
                .await
                .map_err(|e| AppError::SendDataFailed(format!("Failed to flush for connection {}: {}", conn_id, e)))?;

            // 步骤 3c: 记录发送成功的日志。
            info!("{}", format_log_entry(conn_id, "SENT", &data));
            Ok(()) // 发送成功，返回 Ok(()).
        } else {
            // 如果未找到具有给定 conn_id 的连接：
            Err(AppError::ConnectionNotFound(conn_id.to_string())) // 返回连接未找到错误。
        }
        // MutexGuard (conns_guard) 在这里离开作用域，锁被自动释放。
    }

    pub async fn disconnect(&self, conn_id: &str) -> Result<()> {
        // 步骤 1: 获取对连接哈希表的可变访问权限，并尝试移除连接。
        // .lock().await 获取互斥锁。
        let mut conns = self.connections.lock().await;
        // conns.remove(conn_id) 会尝试从哈希表中移除键为 conn_id 的条目。
        // 如果找到并移除，它会返回 Some(ManagedConnection)；否则返回 None。
        if let Some(managed_conn) = conns.remove(conn_id) {
            // 如果连接被成功找到并从哈希表中移除：
            // (此时 MutexGuard `conns` 仍然持有锁，但我们已经取出了 managed_conn)

            // 步骤 2: 发送关闭信号给读取任务。
            // managed_conn.shutdown_tx 是 mpsc 通道的发送端。
            // .send(()).await 异步发送一个空元组作为关闭信号。
            // .is_err() 检查发送操作是否失败 (例如，如果接收端已经关闭，即读取任务已自行终止)。
            if managed_conn.shutdown_tx.send(()).await.is_err() {
                // 如果发送关闭信号失败，记录一个警告。这通常意味着读取任务可能已经因为其他原因 (如错误或对端关闭) 结束了。
                warn!("关闭信号发送失败，读取任务可能已经结束 for {}", conn_id);
            }

            // 步骤 3: 等待读取任务结束，但带有超时。
            // managed_conn.reader_handle 是 Tokio 任务的 JoinHandle。
            // .await 可以用来等待任务完成。
            // tokio::time::timeout 设置了一个2秒的超时来等待任务结束。
            match tokio::time::timeout(std::time::Duration::from_secs(2), managed_conn.reader_handle).await {
                // Ok(Ok(())) 表示：
                // 1. Ok(...): 等待操作在超时时间内完成。
                // 2. Ok(()): reader_handle (JoinHandle) 成功结束，没有返回错误 (因为任务返回类型是 `()`)。
                Ok(Ok(())) => info!("读取任务成功关闭 for {}", conn_id),

                // Ok(Err(join_err)) 表示：
                // 1. Ok(...): 等待操作在超时时间内完成。
                // 2. Err(join_err): reader_handle 任务以错误结束 (例如，任务内部发生 panic)。
                Ok(Err(join_err)) => error!("等待读取任务关闭失败 for {}: {}", conn_id, join_err),

                // Err(_) 表示：
                // 等待读取任务结束的操作超时了。
                Err(_) => warn!("等待读取任务关闭超时 for {}", conn_id),
            }

            // 步骤 4: 记录断开连接的日志。
            info!("{}", format_log_entry(conn_id, "SYSTEM", "Disconnected.".as_bytes()));
            Ok(()) // 断开操作成功完成。
        } else {
            // 如果未找到具有给定 conn_id 的连接：
            Err(AppError::ConnectionNotFound(conn_id.to_string())) // 返回连接未找到错误。
        }
        // MutexGuard (conns) 在这里离开作用域，锁被自动释放。
    }

    pub async fn shutdown_all(&self) {
        // 步骤 1: 获取所有当前连接的 ID。
        // .lock().await 获取互斥锁。
        let conns_guard = self.connections.lock().await;
        // conns_guard.keys() 返回一个迭代器，包含哈希表中所有的键 (连接ID)。
        // .cloned() 克隆每个键 (因为键是 String，需要拥有其副本)。
        // .collect() 将迭代器收集到一个 Vec<String> 中。
        let ids: Vec<String> = conns_guard.keys().cloned().collect();
        // 步骤 2: 显式释放互斥锁。
        // drop(conns_guard) 会立即释放由 conns_guard 持有的锁。
        // 这是很重要的，因为接下来的 self.disconnect(&id).await 也会尝试获取同一个锁。
        // 如果不先释放锁，那么在循环内部调用 disconnect 时会导致死锁 (当前任务持有锁，又尝试获取它)。
        drop(conns_guard);

        // 步骤 3: 遍历所有连接 ID 并尝试断开它们。
        for id in ids {
            info!("正在关闭连接: {}", id);
            // 调用 self.disconnect 来关闭每个连接。
            // .await 因为 disconnect 是异步的。
            if let Err(e) = self.disconnect(&id).await {
                // 如果断开连接时发生错误，记录错误日志。
                error!("关闭连接 {} 时发生错误: {}", id, e);
            }
        }
        info!("所有TCP连接已尝试关闭。");
    }

    // 登录
    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {
        // 1. 连接到服务器
        let conn_id = self.connect(addr.clone(), port).await?;

        // 2. 发送登录数据
        // TODO

        Ok(conn_id)
    }

    // 发送数据给特定单板
    pub async fn send_data_board(&self, conn_id: &str, data_hex: &str, board_id: &str) -> Result<()> {
        // 1. 根据board_id,在data_hex前添加前缀
        let data = board_id.to_owned() + data_hex;

        // 2. 发送数据
        self.send_data(conn_id, data.as_str()).await?;

        Ok(())
    }

    // 从接收到的数据中，分离出board_id和data
    fn extract_board_id_and_data(received_data: &[u8]) -> (String, String) {
        // 将接收到的字节数据进行十六进制编码，转换为字符串
        let received_data_str = hex::encode(received_data);
        // 提取单板ID (前6个字符)
        let board_id = received_data_str.get(0..6).unwrap_or_default();
        // 提取数据部分 (剔除前6个字符后的部分)
        let data = received_data_str.get(6..).unwrap_or_default().to_string();

        (board_id.to_string(), data)
    }

}

#[cfg(test)]
mod tests {
    use super::*; // 导入父模块 (即 TcpManager 所在的模块) 的所有公共项。
    use std::time::Duration; // 用于测试中的延时和超时。
    use tokio::net::TcpListener; // 用于创建测试用的 TCP 服务器。
    use tauri::test::{mock_app, MockRuntime}; // 用于创建模拟的 Tauri AppHandle 和运行时。
    use tauri::AppHandle; // 明确导入 AppHandle，虽然 super::* 也可能导入。

    async fn start_echo_server(addr: &str) -> tokio::task::JoinHandle<()> {
        // TcpListener::bind(addr).await 尝试在指定地址和端口上监听 TCP 连接。
        let listener = TcpListener::bind(addr).await.unwrap(); // .unwrap() 用于测试，如果绑定失败则 panic。
        let addr_owned = addr.to_string(); // 克隆 addr 字符串，因为要在异步块中使用。

        // tokio::spawn 启动一个新的异步任务来运行服务器的接受连接循环。
        tokio::spawn(async move {
            println!("测试回显服务器启动于 {}", addr_owned);
            // 无限循环，持续接受新的客户端连接。
            loop {
                // listener.accept().await 等待并接受一个新的入站 TCP 连接。
                // 返回 Result<(TcpStream, SocketAddr), std::io::Error>。
                match listener.accept().await {
                    Ok((mut socket, _)) => { // _ 忽略客户端地址。
                        println!("测试服务器接受新连接");
                        // 为每个接受的连接再启动一个新的异步任务来处理它。
                        tokio::spawn(async move {
                            // 创建一个缓冲区用于读取数据。
                            let mut buf = [0; 1024];
                            // 无限循环，处理来自这个特定客户端的数据。
                            loop {
                                // socket.read(&mut buf).await 尝试从客户端读取数据。
                                match socket.read(&mut buf).await {
                                    // Ok(0) 表示客户端关闭了连接。
                                    Ok(0) => {
                                        println!("测试服务器连接关闭");
                                        return; // 结束这个客户端处理任务。
                                    }
                                    // Ok(n) 表示成功读取了 n 字节数据。
                                    Ok(n) => {
                                        // socket.write_all(&buf[..n]).await 将读取到的数据原样写回给客户端 (回显)。
                                        if socket.write_all(&buf[..n]).await.is_err() {
                                            println!("测试服务器写入错误");
                                            return; // 写入失败，结束任务。
                                        }
                                        println!("测试服务器回显 {} 字节", n);
                                    }
                                    // Err(e) 表示读取时发生错误。
                                    Err(e) => {
                                        eprintln!("测试服务器读取错误: {}", e);
                                        return; // 读取错误，结束任务。
                                    }
                                }
                            }
                        });
                    }
                    Err(e) => eprintln!("测试服务器接受连接失败: {}", e), // 接受连接失败。
                }
            }
        }) // 返回服务器主任务的 JoinHandle。
    }

    fn create_mock_app_handle() -> AppHandle<MockRuntime> {
        // tauri::test::mock_app() 创建一个模拟的 Tauri 应用实例。
        let app = mock_app();
        // app.handle() 获取该模拟应用的句柄。
        // .clone() 因为 AppHandle 通常是可克隆的 (内部使用 Arc)。
        app.handle().clone()
    }

    // 测试 ping 方法在服务器在线时的情况
    #[tokio::test]
    async fn test_ping_server_online() {
        let server_addr = "127.0.0.1:12345"; // 测试服务器地址。
        // 启动回显服务器。_server_handle 接收 JoinHandle，确保服务器在作用域内运行。
        let _server_handle = start_echo_server(server_addr).await;
        // 短暂休眠，确保服务器有足够的时间启动和绑定端口。
        tokio::time::sleep(Duration::from_millis(100)).await;

        let app_handle = create_mock_app_handle(); // 创建模拟 AppHandle。
        let manager = TcpManager::<MockRuntime>::new(app_handle); // 创建 TcpManager 实例。

        // 调用 ping 方法。
        let result = manager.ping("127.0.0.1".to_string(), 12345).await;
        assert!(result.is_ok()); // 断言 ping 操作本身没有返回错误 (AppError)。
        assert!(result.unwrap()); // 断言 ping 的结果是 true (服务器可达)。
    }

    // 测试 ping 方法在服务器离线时的情况
    #[tokio::test]
    async fn test_ping_server_offline() {
        let app_handle = create_mock_app_handle();
        let manager = TcpManager::<MockRuntime>::new(app_handle);

        // 尝试 ping 一个不太可能运行服务的端口。
        let result = manager.ping("127.0.0.1".to_string(), 54321).await;
        assert!(result.is_ok()); // 断言 ping 操作本身没有返回错误。
        assert!(!result.unwrap()); // 断言 ping 的结果是 false (服务器不可达)。
    }

    // 一个完整的集成测试，覆盖了连接、发送数据、接收数据（通过 Tauri 事件）和断开连接的整个流程
    #[tokio::test]
    async fn test_connect_send_receive_disconnect() {
        let server_addr = "127.0.0.1:12346"; // 测试服务器地址。
        let _server_handle = start_echo_server(server_addr).await; // 启动回显服务器。
        tokio::time::sleep(Duration::from_millis(100)).await; // 等待服务器启动。

        let app_handle = create_mock_app_handle(); // 创建模拟 AppHandle。
        let manager = TcpManager::<MockRuntime>::new(app_handle.clone()); // 创建 TcpManager。

        // 1. 测试连接
        let conn_id_result = manager.connect("127.0.0.1".to_string(), 12346).await;
        assert!(conn_id_result.is_ok()); // 断言连接成功。
        let conn_id = conn_id_result.unwrap(); // 获取连接ID。

        // 2. 设置事件监听以捕获 "tcp_data_received" 事件
        // 创建一个 mpsc 通道，用于在测试代码中接收从事件监听器转发过来的数据。
        let (event_tx, mut event_rx) = mpsc::channel::<TcpDataPayload>(1);
        let conn_id_clone = conn_id.clone(); // 克隆 conn_id 用于闭包。

        // 使用模拟的 app_handle 监听 "tcp_data_received" 事件。
        // 当 TcpManager 通过 app_handle.emit("tcp_data_received", ...) 发送事件时，这个闭包会被调用。
        app_handle.listen("tcp_data_received", move |event| {
            // MockRuntime 的事件 payload 是 &str 类型。
            let payload_str_slice: &str = event.payload();

            // 尝试将 JSON 字符串 payload 解析为 TcpDataPayload 结构体。
            match serde_json::from_str::<TcpDataPayload>(payload_str_slice) {
                Ok(payload_obj) => {
                    // 确保事件是针对我们当前测试的连接ID的。
                    if payload_obj.id == conn_id_clone {
                        // 通过 mpsc 通道将解析后的 payload 发送给测试的主流程。
                        // try_send 是非阻塞的，如果通道已满或关闭会失败。
                        event_tx.try_send(payload_obj).expect("测试事件发送失败");
                    }
                }
                Err(e) => eprintln!("测试：解析事件payload失败 ('{}'): {}", payload_str_slice, e),
            }
        });

        // 3. 测试发送数据
        let test_data_hex = hex::encode("hello server"); // 准备要发送的数据 (十六进制编码)。
        let send_result = manager.send_data(&conn_id, &test_data_hex).await;
        assert!(send_result.is_ok()); // 断言数据发送成功。

        // 4. 测试接收数据 (通过监听到的事件)
        // 等待从 event_rx 通道接收数据，设置2秒超时。
        match tokio::time::timeout(Duration::from_secs(2), event_rx.recv()).await {
            Ok(Some(payload)) => { // 成功接收到数据。
                assert_eq!(payload.id, conn_id); // 断言接收到的数据ID正确。
                assert_eq!(payload.data, test_data_hex); // 断言接收到的数据内容与发送的匹配 (回显服务器会原样返回)。
            }
            Ok(None) => panic!("测试：事件通道关闭，未收到数据"), // 通道意外关闭。
            Err(_) => panic!("测试：等待接收数据超时"), // 超时未收到数据。
        }

        // 5. 测试断开连接
        let disconnect_result = manager.disconnect(&conn_id).await;
        assert!(disconnect_result.is_ok()); // 断言断开连接操作成功。

        // 6. 验证连接已从管理器中移除
        let conns_guard = manager.connections.lock().await; // 获取连接池的锁。
        assert!(!conns_guard.contains_key(&conn_id)); // 断言连接池中不再包含此连接ID。
    }
}