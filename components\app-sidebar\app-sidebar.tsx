"use client"

import React, { useState } from 'react';

// shadcn/ui组件
import {
  Sidebar,
  Sidebar<PERSON>ontent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarTrigger,
  SidebarMenuButton
} from "@/components/ui/sidebar"

// lucide图标
import {
  PlusCircleIcon,
  Forward,
  MoreHorizontal,
  Trash2,
  type LucideIcon,
} from "lucide-react"

// 应用数据
import { route_datas } from "@/app/app-datas";

// dev
import { NavBoards } from "@/components/app-sidebar/nav-boards"
import { NavApps } from "@/components/app-sidebar/nav-apps"
import { ConnSwitcher } from '@/components/app-sidebar/nav-conn';
import { EnvSwitcher } from "@/components/app-sidebar/env-switcher"
import { ModeToggle } from "@/components/theme-switcher/theme-switcher"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [activeItem, setActiveItem] = useState("");
  return (
    <Sidebar collapsible="icon" variant="sidebar" {...props}>
      <SidebarHeader>
        <EnvSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <ConnSwitcher/>
        <NavBoards conn_types={route_datas.conn_types} activeItem={activeItem} setActiveItem={setActiveItem}/>
        <NavApps apps={route_datas.apps} activeItem={activeItem} setActiveItem={setActiveItem}/>
      </SidebarContent>
      <SidebarFooter>
        <SidebarTrigger />
        <ModeToggle />
      </SidebarFooter>
    </Sidebar>
  )
}
