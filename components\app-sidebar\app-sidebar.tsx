"use client"

import React, { useState } from 'react';

// shadcn/ui组件
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarTrigger,
} from "@/components/ui/sidebar"

// 应用数据
import { route_datas } from "@/app/app-datas";

// dev
import { NavBoards } from "@/components/app-sidebar/nav-boards"
import { NavApps } from "@/components/app-sidebar/nav-apps"
import { ConnSwitcher } from '@/components/app-sidebar/nav-conn';
import { EnvSwitcher } from "@/components/app-sidebar/env-switcher"
import { ModeToggle } from "@/components/theme-switcher/theme-switcher"

// hooks
import { useEnvironmentPersistence } from "@/hooks/useEnvironmentPersistence"

// 类型定义
import type { EnvironmentState } from "@/types/env"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [activeItem, setActiveItem] = useState("");
  const [connectionId, setConnectionId] = useState<string | null>(null);

  // 环境状态管理
  const [environmentState, setEnvironmentState] = useState<EnvironmentState>({
    environments: [],
    activeEnvId: null,
  });

  // 使用环境持久化Hook
  useEnvironmentPersistence({
    environmentState,
    setEnvironmentState,
  });

  // 获取当前活动环境
  const activeEnv = environmentState.environments.find(env => env.id === environmentState.activeEnvId) || null;

  return (
    <Sidebar collapsible="icon" variant="sidebar" {...props}>
      <SidebarHeader>
        <EnvSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <ConnSwitcher
          activeEnv={activeEnv}
          onConnectionChange={setConnectionId}
        />
        <NavBoards
          conn_types={route_datas.conn_types}
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          connectionId={connectionId}
        />
        <NavApps apps={route_datas.apps} activeItem={activeItem} setActiveItem={setActiveItem}/>
      </SidebarContent>
      <SidebarFooter>
        <SidebarTrigger />
        <ModeToggle />
      </SidebarFooter>
    </Sidebar>
  )
}
