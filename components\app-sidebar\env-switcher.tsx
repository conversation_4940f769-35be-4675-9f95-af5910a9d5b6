// components/app-sidebar/env-switcher.tsx
"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Plus, Trash2, FolderPen, Layers, Wifi, WifiOff, Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Button } from "../ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { pingTcpServer } from "@/components/utils/env-utils"
import { useEnvironmentPersistence } from "@/hooks/useEnvironmentPersistence"
import type {
  Environment,
  EnvironmentState,
  NewEnvironmentForm,
  EnvironmentFormErrors,
  PingResult
} from "@/types/env"

export function EnvSwitcher() {
  const { isMobile } = useSidebar()
  const { toast } = useToast()

  // 环境状态管理
  const [environmentState, setEnvironmentState] = React.useState<EnvironmentState>({
    environments: [],
    activeEnvId: null,
  })

  // 使用环境持久化Hook
  useEnvironmentPersistence({
    environmentState,
    setEnvironmentState,
  })

  // UI状态
  const [, setOpen] = React.useState(false)
  const [showNewEnvDialog, setShowNewEnvDialog] = React.useState(false)
  const [showRenameDialog, setShowRenameDialog] = React.useState(false)
  const [selectedEnvForAction, setSelectedEnvForAction] = React.useState<Environment | null>(null)

  // 表单状态
  const [newEnvForm, setNewEnvForm] = React.useState<NewEnvironmentForm>({
    name: '',
    addr: '',
    port: '12345',
    username: 'admin',
    password: '',
  })
  const [formErrors, setFormErrors] = React.useState<EnvironmentFormErrors>({})
  const [isPinging, setIsPinging] = React.useState(false)
  const [pingResult, setPingResult] = React.useState<PingResult | null>(null)

  // 重命名相关状态
  const [renameForm, setRenameForm] = React.useState({ name: '' })
  const [renameError, setRenameError] = React.useState('')

  // 连接状态 (暂时未使用，为将来功能预留)
  // const [connectionStatus, setConnectionStatus] = React.useState<ConnectionStatus[]>([])

  // 获取当前活动环境
  const activeEnv = environmentState.environments.find(env => env.id === environmentState.activeEnvId)

  // 验证IP地址和端口的共用函数
  const validateAddressAndPort = React.useCallback((addr: string, port: string) => {
    const errors: { addr?: string; port?: string } = {}

    // IP地址格式验证：
    // 1. 不能为空
    // 2. 必须符合IPv4格式（例如：***********）
    // 3. 不能以0开头
    // 4. 每个数字段必须在0-255范围内
    const ipRegex = /^(?!0)(?!.*\.$)((1?\d?\d|25[0-5]|2[0-4]\d)(\.|$)){4}$/
    if (!addr.trim()) {
      errors.addr = 'IP地址不能为空'
    } else if (!ipRegex.test(addr.trim())) {
      errors.addr = 'IP地址格式不正确'
    }

    // 端口号验证：
    // 1. 不能为空
    // 2. 必须是数字
    // 3. 必须在1-65535范围内
    if (!port.trim()) {
      errors.port = '端口不能为空'
    } else {
      const portNum = parseInt(port.trim())
      if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
        errors.port = '端口号必须在1-65535之间'
      }
    }

    return errors
  }, [])

  // 处理函数
  const handleSelectEnvironment = React.useCallback((env: Environment) => {
    setEnvironmentState(prev => ({
      ...prev,
      activeEnvId: env.id
    }))
    setOpen(false)
  }, [setEnvironmentState])

  const handleAddEnvironment = React.useCallback(async () => {
    // 验证表单
    const errors: EnvironmentFormErrors = {}
    if (!newEnvForm.name.trim()) errors.name = '环境名称不能为空'

    // 使用共用验证函数验证IP地址和端口
    const addressPortErrors = validateAddressAndPort(newEnvForm.addr, newEnvForm.port)
    Object.assign(errors, addressPortErrors)

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    const port = parseInt(newEnvForm.port.trim())

    try {
      // 创建新环境
      const newEnv: Environment = {
        id: `env_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        name: newEnvForm.name.trim(),
        addr: newEnvForm.addr.trim(),
        port: port,
        username: newEnvForm.username.trim() || undefined,
        password: newEnvForm.password.trim() || undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // 添加到环境列表
      setEnvironmentState(prev => ({
        ...prev,
        environments: [...prev.environments, newEnv],
        activeEnvId: newEnv.id // 自动激活新添加的环境
      }))

      // 重置表单
      setNewEnvForm({
        name: '',
        addr: '',
        port: '12345',
        username: 'admin',
        password: '',
      })
      setFormErrors({})
      setPingResult(null)
      setShowNewEnvDialog(false)
      setOpen(false)

      toast({
        title: "环境添加成功",
        description: `环境 "${newEnv.name}" 已添加`,
        duration: 2000,
      })

    } catch (error) {
      toast({
        title: "添加环境失败",
        description: `${error}`,
        variant: "destructive",
        duration: 5000,
      })
    }
  }, [newEnvForm, setEnvironmentState, setFormErrors, setPingResult, setShowNewEnvDialog, setOpen, toast, validateAddressAndPort])

  const handlePing = React.useCallback(async () => {
    // 清除之前的ping结果
    setPingResult(null)

    // 使用共用验证函数验证IP地址和端口
    const addressPortErrors = validateAddressAndPort(newEnvForm.addr, newEnvForm.port)

    // 将验证错误设置到表单错误中，显示在输入框下方
    if (addressPortErrors.addr || addressPortErrors.port) {
      setFormErrors(prev => ({
        ...prev,
        addr: addressPortErrors.addr,
        port: addressPortErrors.port
      }))
      return
    }

    // 清除表单错误（如果验证通过）
    setFormErrors(prev => ({
      ...prev,
      addr: undefined,
      port: undefined
    }))

    const port = parseInt(newEnvForm.port.trim())

    setIsPinging(true)

    try {
      const startTime = Date.now()
      const success = await pingTcpServer(newEnvForm.addr.trim(), port)
      const responseTime = Date.now() - startTime

      setPingResult({
        success,
        responseTime: success ? responseTime : undefined,
        error: success ? undefined : '连接失败'
      })

    } catch (error) {
      // 显示后端返回的具体错误信息
      setPingResult({
        success: false,
        error: `${error}`
      })
    } finally {
      setIsPinging(false)
    }
  }, [newEnvForm.addr, newEnvForm.port, validateAddressAndPort])

  const handleDeleteEnvironment = React.useCallback((env: Environment) => {
    setEnvironmentState(prev => {
      const newEnvironments = prev.environments.filter(e => e.id !== env.id)
      return {
        environments: newEnvironments,
        activeEnvId: prev.activeEnvId === env.id
          ? (newEnvironments.length > 0 ? newEnvironments[0].id : null)
          : prev.activeEnvId
      }
    })

    toast({
      title: "环境已删除",
      description: `环境 "${env.name}" 已删除`,
      duration: 2000,
    })
  }, [setEnvironmentState, toast])

  const handleRenameEnvironment = React.useCallback(async () => {
    if (!selectedEnvForAction) return

    // 验证新名称
    const newName = renameForm.name.trim()
    if (!newName) {
      setRenameError('环境名称不能为空')
      return
    }

    // 检查名称是否与其他环境重复
    const isDuplicate = environmentState.environments.some(
      env => env.id !== selectedEnvForAction.id && env.name === newName
    )
    if (isDuplicate) {
      setRenameError('环境名称已存在')
      return
    }

    try {
      // 更新环境名称
      setEnvironmentState(prev => ({
        ...prev,
        environments: prev.environments.map(env =>
          env.id === selectedEnvForAction.id
            ? { ...env, name: newName, updatedAt: new Date().toISOString() }
            : env
        )
      }))

      // 重置状态
      setShowRenameDialog(false)
      setSelectedEnvForAction(null)
      setRenameForm({ name: '' })
      setRenameError('')

      toast({
        title: "重命名成功",
        description: `环境已重命名为 "${newName}"`,
        duration: 2000,
      })

    } catch (error) {
      toast({
        title: "重命名失败",
        description: `${error}`,
        variant: "destructive",
        duration: 5000,
      })
    }
  }, [selectedEnvForAction, renameForm.name, environmentState.environments, setEnvironmentState, toast])

  // 打开重命名对话框时初始化表单
  React.useEffect(() => {
    if (showRenameDialog && selectedEnvForAction) {
      setRenameForm({ name: selectedEnvForAction.name })
      setRenameError('')
    }
  }, [showRenameDialog, selectedEnvForAction])

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <Popover>

          {/* 环境信息切换按钮 */}
          <PopoverTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Layers className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {activeEnv?.name || '未选择'}
                </span>
                <span className="truncate text-xs">{activeEnv?.addr || '请点击此处选择环境'}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </PopoverTrigger>

          {/* 弹出层: 环境信息 */}
          <PopoverContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg pb-1"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            {/* 标题 */}
            <span className="text-xs text-muted-foreground">Envs</span>
            <Command>
              {/* 搜索框 */}
              <CommandInput placeholder="搜索环境名称或IP地址..." />
              {/* 搜索结果 */}
              <CommandList>
                <CommandEmpty>没找到呃..</CommandEmpty>
                <CommandGroup>
                  {environmentState.environments.map((env) => (
                    // 单个环境项
                    <CommandItem
                      key={env.id}
                      value={`${env.name} ${env.addr} ${env.port}`}
                      keywords={[env.name, env.addr, `${env.addr}:${env.port}`, env.port.toString()]}
                      onSelect={() => {
                        handleSelectEnvironment(env);
                      }}
                      className="gap-2 p-2"
                    >
                      <ContextMenu>
                        {/* 单个环境项显示内容(项可勾选,且可触发右键菜单) */}
                        <ContextMenuTrigger className="flex w-full items-center">
                          <div className="flex size-6 items-center justify-center rounded-sm mr-2">
                            <Layers className="size-4 shrink-0" />
                          </div>
                          <div className="flex flex-col flex-1 min-w-0">
                            <span className="text-sm font-medium truncate">{env.name}</span>
                            <span className="text-xs text-muted-foreground truncate">{env.addr}:{env.port}</span>
                          </div>
                          <Check
                            className={cn(
                              "ml-auto shrink-0",
                              activeEnv?.id === env.id
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </ContextMenuTrigger>
                        {/* 右键菜单 */}
                        <ContextMenuContent className="w-48 rounded-lg">
                          <ContextMenuItem
                            onClick={() => {
                              setSelectedEnvForAction(env)
                              setShowRenameDialog(true)
                            }}
                          >
                            <FolderPen className="text-muted-foreground mr-2" strokeWidth={1.5} size={18} />
                            <span>重命名</span>
                          </ContextMenuItem>
                          <ContextMenuItem
                            onClick={() => handleDeleteEnvironment(env)}
                          >
                            <Trash2 className="text-muted-foreground mr-2" strokeWidth={1.5} size={18} />
                            <span>删除</span>
                          </ContextMenuItem>
                        </ContextMenuContent>
                      </ContextMenu>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
              {/* 水平分隔线 */}
              <CommandSeparator />
              {/* 添加新环境信息组件 */}
              <CommandList>
                <CommandGroup>
                  <Dialog open={showNewEnvDialog} onOpenChange={setShowNewEnvDialog}>
                    {/* 添加新环境信息的触发按钮 */}
                    <DialogTrigger asChild>
                      <CommandItem
                        onSelect={() => {
                          setOpen(false)
                          setShowNewEnvDialog(true)
                        }}
                      >
                        <Plus className="size-4" />
                        添加新的环境信息
                      </CommandItem>
                    </DialogTrigger>
                    {/* 添加新环境信息的弹出框 */}
                    <DialogContent className="w-[350px]">
                      <DialogHeader>
                        <DialogTitle>新环境</DialogTitle>
                        <DialogDescription className="text-xs">
                          添加一个新的环境信息<br />以便连接其主控界面或单板串口
                        </DialogDescription>
                      </DialogHeader>
                      <div>
                        <div className="space-y-2 py-1 pb-4">
                          <div className="space-y-1">
                            <Label htmlFor="env-name">环境名称</Label>
                            <Input
                              id="env-name"
                              placeholder="输入环境名称"
                              value={newEnvForm.name}
                              onChange={(e) => {
                                  setNewEnvForm(prev => ({ ...prev, name: e.target.value }))
                                  // 清除名称相关的错误
                                  if (formErrors.name) {
                                    setFormErrors(prev => ({ ...prev, name: undefined }))
                                  }
                                }}
                              className={formErrors.name ? "border-red-500" : ""}
                            />
                            {formErrors.name && <p className="text-xs text-red-500">{formErrors.name}</p>}
                          </div>
                          <div className="space-y-1">
                            <Label htmlFor="env-addr">IP地址</Label>
                            <div className="grid grid-cols-12 gap-2 w-full items-center">
                              <Input
                                className={`col-span-7 ${formErrors.addr ? "border-red-500" : ""}`}
                                id="env-addr"
                                placeholder="输入环境的IP地址"
                                value={newEnvForm.addr}
                                onChange={(e) => {
                                  setNewEnvForm(prev => ({ ...prev, addr: e.target.value }))
                                  // 清除IP地址相关的错误和ping结果
                                  if (formErrors.addr) {
                                    setFormErrors(prev => ({ ...prev, addr: undefined }))
                                  }
                                  setPingResult(null)
                                }}
                                pattern="^(?!0)(?!.*\.$)((1?\d?\d|25[0-5]|2[0-4]\d)(\.|$)){4}$"
                              />
                              <Input
                                className={`col-span-3 ${formErrors.port ? "border-red-500" : ""}`}
                                id="env-port"
                                placeholder="端口"
                                value={newEnvForm.port}
                                onChange={(e) => {
                                  setNewEnvForm(prev => ({ ...prev, port: e.target.value }))
                                  // 清除端口相关的错误和ping结果
                                  if (formErrors.port) {
                                    setFormErrors(prev => ({ ...prev, port: undefined }))
                                  }
                                  setPingResult(null)
                                }}
                              />
                              <Button
                                className="col-span-2"
                                onClick={handlePing}
                                disabled={isPinging}
                                size="sm"
                              >
                                {isPinging ? <Loader2 className="size-3 animate-spin" /> : "Ping"}
                              </Button>
                            </div>
                            {formErrors.addr && <p className="text-xs text-red-500">{formErrors.addr}</p>}
                            {formErrors.port && <p className="text-xs text-red-500">{formErrors.port}</p>}
                            {pingResult && (
                              <div className={`text-xs flex items-center gap-1 ${pingResult.success ? 'text-green-600' : 'text-red-500'}`}>
                                {pingResult.success ? <Wifi className="size-3" /> : <WifiOff className="size-3" />}
                                {pingResult.success
                                  ? `连接成功 (${pingResult.responseTime}ms)`
                                  : `${pingResult.error}`
                                }
                              </div>
                            )}
                          </div>
                          <div className="space-y-1">
                            <Label htmlFor="env-username">用户名</Label>
                            <Input
                              id="env-username"
                              value={newEnvForm.username}
                              onChange={(e) => setNewEnvForm(prev => ({ ...prev, username: e.target.value }))}
                            />
                          </div>
                          <div className="space-y-1">
                            <Label htmlFor="env-password">密码</Label>
                            <Input
                              id="env-password"
                              type="password"
                              value={newEnvForm.password}
                              onChange={(e) => setNewEnvForm(prev => ({ ...prev, password: e.target.value }))}
                            />
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setShowNewEnvDialog(false)}>
                          取消
                        </Button>
                        <Button onClick={handleAddEnvironment}>添加</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </SidebarMenuItem>

      {/* 重命名环境对话框 */}
      <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <DialogContent className="w-[350px]">
          <DialogHeader>
            <DialogTitle>重命名环境</DialogTitle>
            <DialogDescription className="text-xs">
              修改环境 &ldquo;{selectedEnvForAction?.name}&rdquo; 的名称
            </DialogDescription>
          </DialogHeader>
          <div>
            <div className="space-y-2 py-1 pb-4">
              <div className="space-y-1">
                <Label htmlFor="rename-env-name">环境名称</Label>
                <Input
                  id="rename-env-name"
                  placeholder="输入新的环境名称"
                  value={renameForm.name}
                  onChange={(e) => {
                    setRenameForm({ name: e.target.value })
                    // 清除错误信息
                    if (renameError) {
                      setRenameError('')
                    }
                  }}
                  className={renameError ? "border-red-500" : ""}
                  autoFocus
                />
                {renameError && <p className="text-xs text-red-500">{renameError}</p>}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowRenameDialog(false)
                setSelectedEnvForAction(null)
                setRenameForm({ name: '' })
                setRenameError('')
              }}
            >
              取消
            </Button>
            <Button onClick={handleRenameEnvironment}>确认</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </SidebarMenu>
  )
}
