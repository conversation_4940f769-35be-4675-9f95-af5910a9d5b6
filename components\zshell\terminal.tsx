"use client";

import { useTheme } from "next-themes";
import Editor from "@monaco-editor/react";

const EDITOR_OPTIONS = {
    minimap: { enabled: false },
    fontSize: 14,
    fontFamily: "var(--font-0xproto)",
    lineNumbers: "off",
    automaticLayout: true,
    formatOnPaste: true,
    formatOnType: true,
    padding: { top: 8, bottom: 8 },
    mouseWheelZoom: true,
    readOnly: true,
    domReadOnly: true,
    scrollBeyondLastLine: false,
    scrollbar: {
        vertical: 'visible',
        horizontal: 'visible',
        useShadows: false,
        verticalScrollbarSize: 10,
        horizontalScrollbarSize: 10
    }
} as const;

export function Terminal() {
    // 主题
    const { resolvedTheme } = useTheme();
    const editorTheme = resolvedTheme === "dark" ? "vs-dark" : "light";
    return (
        <Editor
            height="100%"
            language="txt"
            theme={editorTheme}
            value="✓ Compiled in 161ms (1720 modules) ✓ Compiled in 161ms (1720 modules)"
            options={{
                ...EDITOR_OPTIONS,
            }}
        />
    );
}